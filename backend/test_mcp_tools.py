#!/usr/bin/env python3
"""
MCP Server Tool Discovery Test Script

This script tests what tools are returned from MCP servers to help debug
tool discovery issues with HTTP MCP servers like Gmail and Notion.

Usage:
    python test_mcp_tools.py
    
Then enter the MCP server URL when prompted.
"""

import asyncio
import json
import sys
from typing import Dict, List, Any, Optional
import httpx
from urllib.parse import urlparse

class MCPToolTester:
    """Test tool discovery for MCP servers."""
    
    def __init__(self):
        self.timeout = 30
        
    async def test_mcp_server(self, url: str, auth_headers: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Test tool discovery for an MCP server.
        
        Args:
            url: The MCP server URL
            auth_headers: Optional authentication headers
            
        Returns:
            Dictionary with test results
        """
        print(f"\n🔍 Testing MCP server: {url}")
        print("=" * 60)
        
        results = {
            "url": url,
            "success": False,
            "tools": [],
            "tool_count": 0,
            "error": None,
            "response_data": None
        }
        
        try:
            # Parse URL to validate
            parsed = urlparse(url)
            if not parsed.scheme or not parsed.netloc:
                raise ValueError("Invalid URL format")
                
            # Prepare headers
            headers = {
                "Content-Type": "application/json",
                "Accept": "application/json",
                "User-Agent": "Atlas-MCP-Tester/1.0"
            }
            
            if auth_headers:
                headers.update(auth_headers)
                
            # Prepare MCP request payload for tool discovery
            payload = {
                "jsonrpc": "2.0",
                "id": "test-tools-request",
                "method": "tools/list",
                "params": {}
            }
            
            print(f"📤 Sending request to: {url}")
            print(f"📋 Payload: {json.dumps(payload, indent=2)}")
            print(f"🔑 Headers: {json.dumps({k: v for k, v in headers.items() if 'auth' not in k.lower()}, indent=2)}")
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(url, json=payload, headers=headers)
                
                print(f"\n📥 Response Status: {response.status_code}")
                print(f"📥 Response Headers: {dict(response.headers)}")
                
                if response.status_code == 200:
                    response_data = response.json()
                    results["response_data"] = response_data
                    
                    print(f"📥 Raw Response: {json.dumps(response_data, indent=2)}")
                    
                    # Parse tools from response
                    if "result" in response_data and "tools" in response_data["result"]:
                        tools = response_data["result"]["tools"]
                        results["tools"] = tools
                        results["tool_count"] = len(tools)
                        results["success"] = True
                        
                        print(f"\n✅ Successfully discovered {len(tools)} tools:")
                        print("-" * 40)
                        
                        for i, tool in enumerate(tools, 1):
                            tool_name = tool.get("name", "Unknown")
                            tool_desc = tool.get("description", "No description")
                            print(f"{i:2d}. {tool_name}")
                            print(f"    Description: {tool_desc}")
                            
                            # Show input schema if available
                            if "inputSchema" in tool:
                                schema = tool["inputSchema"]
                                if "properties" in schema:
                                    props = list(schema["properties"].keys())
                                    print(f"    Parameters: {', '.join(props[:5])}")
                                    if len(props) > 5:
                                        print(f"                ... and {len(props) - 5} more")
                            print()
                            
                    else:
                        results["error"] = "No tools found in response"
                        print(f"❌ No tools found in response structure")
                        print(f"Response keys: {list(response_data.keys())}")
                        
                else:
                    error_text = response.text
                    results["error"] = f"HTTP {response.status_code}: {error_text}"
                    print(f"❌ HTTP Error {response.status_code}")
                    print(f"Response: {error_text}")
                    
        except httpx.TimeoutException:
            results["error"] = f"Request timeout after {self.timeout}s"
            print(f"⏰ Request timeout after {self.timeout}s")
            
        except httpx.RequestError as e:
            results["error"] = f"Request error: {str(e)}"
            print(f"🌐 Request error: {str(e)}")
            
        except json.JSONDecodeError as e:
            results["error"] = f"JSON decode error: {str(e)}"
            print(f"📄 JSON decode error: {str(e)}")
            
        except Exception as e:
            results["error"] = f"Unexpected error: {str(e)}"
            print(f"💥 Unexpected error: {str(e)}")
            
        return results
    
    def print_summary(self, results: Dict[str, Any]):
        """Print a summary of the test results."""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        
        print(f"URL: {results['url']}")
        print(f"Success: {'✅ Yes' if results['success'] else '❌ No'}")
        print(f"Tools Found: {results['tool_count']}")
        
        if results['error']:
            print(f"Error: {results['error']}")
            
        if results['tools']:
            print(f"\nTool Names:")
            for tool in results['tools']:
                print(f"  • {tool.get('name', 'Unknown')}")

async def main():
    """Main function to run the MCP tool tester."""
    print("🧪 MCP Server Tool Discovery Tester")
    print("=" * 60)
    print("This script tests what tools are returned from MCP servers.")
    print("Useful for debugging tool discovery issues.\n")
    
    tester = MCPToolTester()
    
    while True:
        try:
            # Get URL from user
            url = input("Enter MCP server URL (or 'quit' to exit): ").strip()
            
            if url.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
                
            if not url:
                print("❌ Please enter a valid URL")
                continue
                
            # Ask for auth headers if needed
            auth_headers = {}
            add_auth = input("Add authentication headers? (y/n): ").strip().lower()
            
            if add_auth in ['y', 'yes']:
                print("Enter headers in format 'key: value' (empty line to finish):")
                while True:
                    header = input("Header: ").strip()
                    if not header:
                        break
                    if ':' in header:
                        key, value = header.split(':', 1)
                        auth_headers[key.strip()] = value.strip()
                    else:
                        print("Invalid format. Use 'key: value'")
            
            # Run the test
            results = await tester.test_mcp_server(url, auth_headers if auth_headers else None)
            
            # Print summary
            tester.print_summary(results)
            
            # Ask if user wants to save results
            save = input("\nSave results to file? (y/n): ").strip().lower()
            if save in ['y', 'yes']:
                filename = f"mcp_test_results_{int(asyncio.get_event_loop().time())}.json"
                with open(filename, 'w') as f:
                    json.dump(results, f, indent=2)
                print(f"💾 Results saved to {filename}")
                
            print("\n" + "-" * 60)
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"💥 Error: {str(e)}")

if __name__ == "__main__":
    # Example URLs for quick testing (commented out)
    print("Example MCP URLs:")
    print("• Gmail: https://mcp.composio.dev/partner/composio/gmail/mcp?customerId=YOUR_ID")
    print("• Notion: https://mcp.composio.dev/partner/composio/notion/mcp?customerId=YOUR_ID")
    print()
    
    asyncio.run(main())
